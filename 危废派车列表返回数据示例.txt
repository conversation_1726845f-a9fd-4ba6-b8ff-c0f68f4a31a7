{"actions": [{"action": "", "actionParam": "{\"update\":{\"if\":{\"bpm_status\":[\"finish\",\"contact\"]},\"then\":{\"bpm_status\":\"contact\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "1", "icon": "glyphicon-phone-alt", "id": "01784813ea808a2876187847ee55028a", "key": "", "legend": "", "list": "waste_id,package_type,security_measure,outer_goal,handle_type,five_bills_code,produce_enterprise,transfer_enterprise,dispose_enterprise,transfer_person,car_id,card_id,lock_id,transfer_start_position,transfer_end_position,is_send_info", "method": "PUT", "name": "联系外运", "primary": true, "rule": "", "selected": "single", "view": ""}, {"action": "", "actionParam": "", "allowMobile": false, "controls": "", "display": "", "group": "", "icon": "glyphicon-inbox", "id": "********************************", "key": "recontact", "legend": "", "list": "five_bills_code,produce_enterprise,transfer_enterprise,dispose_enterprise,transfer_person,car_id,card_id,lock_id,transfer_start_position,transfer_end_position,is_send_info", "method": "PUT", "name": "再次派车", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "e.hwms-change-transfer", "actionParam": "", "allowMobile": false, "controls": "", "display": "", "group": "", "icon": "glyphicon-random", "id": "01784813ea848a2876187847ee55028c", "key": "change", "legend": "", "list": "", "method": "POST", "name": "固废明细转单", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "", "actionParam": "{\"before\":{\"and\":{\".bpm_status\":[\"out-store\",\"weight\"]}}}", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-import", "id": "01784813ea868a2876187847ee55028d", "key": "weight", "legend": "", "list": "weight_record_code,weight,is_send_info,five_bills_code", "method": "PUT", "name": "过磅", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "e.hwms-contacts-order", "actionParam": "{\"update\":{\"if\":{\"bpm_status\":[\"weight\",\"print\"]},\"then\":{\"bpm_status\":\"print\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "1", "icon": "glyphicon-file", "id": "01784813ea888a2876187847ee55028e", "key": "print", "legend": "", "list": "", "method": "POST", "name": "联单打印", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "e.hwms-contacts-order-out", "actionParam": "{\"update\":{\"if\":{\"bpm_status\":[\"weight\",\"print\"]},\"then\":{\"bpm_status\":\"print\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "", "icon": "glyphicon-file", "id": "01784813ea8a8a2876187847ee55028f", "key": "print-n-out", "legend": "", "list": "", "method": "POST", "name": "宁波省外联单打印", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "e.hwms-contacts-order-in", "actionParam": "{\"update\":{\"if\":{\"bpm_status\":[\"weight\",\"print\"]},\"then\":{\"bpm_status\":\"print\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "", "icon": "glyphicon-file", "id": "01784813ea8c8a2876187847ee550290", "key": "print-n-in", "legend": "", "list": "", "method": "POST", "name": "宁波省内联单打印", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "file", "actionParam": "{\"before\":{\"and\":{\"bpm_status\":[\"print\"]}},\"update\":{\"if\":{\"bpm_status\":[\"print\"]},\"then\":{\"bpm_status\":\"upload\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "", "icon": "glyphicon-plus", "id": "01784813ea8d8a2876187847ee550291", "key": "upload", "legend": "", "list": "", "method": "POST", "name": "扫描件上传", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "one", "actionParam": "{\"update\":{\"if\":{\"bpm_status\":[\"upload\"]},\"then\":{\"bpm_status\":\"close\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "1", "icon": "glyphicon-remove-sign", "id": "01784813ea8f8a2876187847ee550292", "key": "close", "legend": "", "list": "", "method": "POST", "name": "关闭", "primary": false, "rule": "", "selected": "single", "view": ""}], "data": {"collection": [{"transfer_id": "017954dd5dda8a28b2aa7954c2f5006f", "apply_code": "202105100006", "user_name": "吴银威", "org_name": "MDI装置大横班乙班", "parent_org_name": "宁波MDI装置", "hwms_org_id": "**********", "apply_date": 1620576000000, "apply_type": "equ", "is_plan": "1", "transfer_type": "outer", "is_sales": "0", "phone": "642589", "plan_transfer_quantity": "2", "plan_transfer_time": 1620662400000, "duty_person": "尤红军", "transfer_position": "暂存区", "waste_id": "0157275198678a8ae704570e3d621f9f", "waste_name": "污水处理污泥", "category_code": "565-104-13", "parent_category_code": "HW13", "report_group_name": "污水处理污泥", "risk": "毒性", "waste_modal": "固态/液态", "harmful_ingredient": "磷酸钙,硅酸铝", "package_type": "桶装", "security_measure": "防火、防爆、防漏", "outer_goal": "处置", "handle_type": "3", "produce_enterprise": "0157089194828a8ae70456f9ad0710a3", "transfer_enterprise": "01575a100f718a8ae70457565eca0300", "dispose_enterprise": "0156695406328a8afbb3566926740034", "five_bills_code": "TEST202105100001", "transfer_person": "尹若明", "car_id": "0156789405208a8afbb45673e5b40110", "card_id": "2B18722C", "card_code": "车卡10_宁波容威", "card_name": "宁波容威车卡10", "lock_id": "", "transfer_start_position": "起点", "transfer_end_position": "终点", "note": "test", "wait_count": "2", "pass_count": "0", "back_count": "0", "bpm_status": "contact", "update_time": 1620627709695, "is_send_info": "1", "year": "2021", "company_id": "00000017", "division_id": "00000017"}], "count": 1, "exists": false, "pagination": {"count": 1, "current": 1, "paging": true, "size": 15}}, "extend": "", "handle": "mulpitle", "icon": "", "id": "01784813e9568a2876187847ee550231", "layout": "", "legend": "视图", "mode": "table", "name": "固废外运处置（按事业部）", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "转移主键", "control": "", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "transfer_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "申请单号", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "基础信息", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "apply_code", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "申请人", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "user_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "工序", "control": "", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "装置", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "parent_org_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "组织", "column": 6, "control": "view-select", "dataType": "wordbook", "dictMode": "bydata", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "hwms_org_id", "params": {"control": {"displayValue": "org_name", "realValue": "org_id", "url": "/hwms/api/hwms-org-select"}}, "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"org_alias": "", "org_id": "**********", "org_name": "MDI装置大横班乙班", "org_stitle": "", "parent_org_id": "00000436", "status": ""}], "count": 1, "exists": false}, "extend": "", "handle": "single", "icon": "", "id": "01626be7b4838a8afbb86266191b01e8", "layout": "", "legend": "", "mode": "tree", "name": "HWMS虚拟组织机构字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "组织主键", "control": "", "dataType": "uuid", "group": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "org_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织名称", "control": "text", "dataType": "string", "display": "title", "events": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "组织别名", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_alias", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "组织简称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_stitle", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "父组织", "control": "", "dataType": "wordbook", "display": "", "events": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "parent_org_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织排序", "control": "text", "dataType": "integer", "group": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "sort_num", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "数据状态", "control": "select", "dataType": "string", "display": "", "events": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "org_id", "nodeIdName": "org_id", "parentIdName": "parent_org_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "申请日期", "dataType": "date", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "apply_date", "remind": "", "required": true, "system": "<PERSON><PERSON>ly", "type": "value", "unique": false, "value": "1756137600000", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "申请类型", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "固废转移", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "equ:装置,station:固废站", "maxLength": 64, "metaType": "String", "name": "apply_type", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "是否计划内", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "1:是,0:否", "maxLength": 64, "metaType": "String", "name": "is_plan", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "转移类型", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "inner:转移至固废站,outer:外运", "maxLength": 64, "metaType": "String", "name": "transfer_type", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "内部转移", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "处置去向", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "0:直接外委处置,1:有价值处置,2:环保科技处置,3:无价值处置", "maxLength": 64, "metaType": "String", "name": "is_sales", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "0", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "联系电话", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "phone", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "预计转移量", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "plan_transfer_quantity", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "预计转移时间", "control": "datetime", "dataType": "date", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "plan_transfer_time", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "实际转移时间", "column": 6, "control": "datetime", "dataType": "time", "dictMode": "all", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "transfer_time", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "转移联系人", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "duty_person", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "实际操作人", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "operator", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "废物产生位置", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "transfer_position", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "固废名称", "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "waste_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [{"group": "1", "i18nCode": "", "icon": "glyphicon-plus", "id": "015493ed680e8a8ae61a538908b4432c", "key": "", "method": "POST", "name": "新增", "primary": true, "selected": "none"}, {"group": "1", "i18nCode": "", "icon": "glyphicon-pencil", "id": "015493ed68138a8ae61a538908b4432d", "key": "", "method": "PUT", "name": "修改", "primary": false, "selected": "single"}, {"group": "", "i18nCode": "", "icon": "glyphicon-trash", "id": "015493ed68178a8ae61a538908b4432e", "key": "", "method": "DELETE", "name": "删除", "primary": false, "selected": "somewhat"}], "data": {"collection": [{"category_id": "01598271133e8a8ae703596dfa3528e0", "report_group_id": "0159866e4cf48a8ae704596df9892c6f", "status": "1", "waste_id": "0157275198678a8ae704570e3d621f9f", "waste_name": "污水处理污泥", "waste_source": "宁波容威装置污水预物化处理生产污泥"}], "count": 1, "exists": false}, "extend": "", "handle": "mulpitle", "icon": "", "id": "015493ed67c68a8ae61a538908b4431b", "layout": "", "legend": "视图", "mode": "table", "name": "固废名称字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "固废主键", "control": "", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "waste_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "固废名称", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "waste_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "固废分类", "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "category_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "固废分组", "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "report_group_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "固废来源", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "waste_source", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "状态", "control": "select", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "1:有效,0:无效", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "有效", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "序号", "control": "text", "dataType": "integer", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "sort_num", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "waste_id", "nodeIdName": "waste_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "固废名称", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "固废信息", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "waste_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "固废代码", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "category_code", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "固废类别", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "parent_category_code", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "报批分组", "control": "", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "report_group_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "危险特性", "control": "multiple", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "毒性,腐蚀性,易燃性,反应性,感染性", "maxLength": 64, "metaType": "String", "name": "risk", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "固废形态", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "waste_modal", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "主要有害成分", "control": "multiple", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "harmful_ingredient", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "write", "alias": "包装方式", "column": 6, "control": "multiple", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "袋装,桶装,箱装,空桶,槽罐,管道运输,其它", "maxLength": 64, "metaType": "String", "name": "package_type", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "禁忌与应急措施", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "security_measure", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "防火、防爆、防漏", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "外运目的", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "外运信息", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "中转贮存,利用,处理,处置,其他", "maxLength": 64, "metaType": "String", "name": "outer_goal", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "处置", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "废物处置方式", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "20:D1-填埋,21:D9-物理化学处理,22:D10-焚烧,23:R2-溶剂回收/再生（如蒸馏、萃取等）,24:R3-再循环/再利用不是用作溶剂的有机物,25:R4-再循环/再利用金属和金属化合物,26:R5-再循环/再利用其他无机物,27:R6-再生酸或碱,28:R7-回收污染减除剂的组分,29:R8-回收催化剂组分,30:R9-废油再提炼或其他废油的再利用,31:R15-其他,32:C1-水泥窑共处置,33:C5-收集废物", "maxLength": 64, "metaType": "String", "name": "handle_type", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "产生企业", "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "produce_enterprise", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"enterprise_id": "0157089194828a8ae70456f9ad0710a3", "enterprise_name": "万华化学（宁波）有限公司", "enterprise_property": "国有", "enterprise_type": "produce", "industry_code": ""}], "count": 1, "exists": false}, "extend": "", "handle": "single", "icon": "", "id": "016bd439080e8a28906d6bcf6b81001f", "layout": "", "legend": "视图", "mode": "table", "name": "企业字典（产生企业）", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "企业主键", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "enterprise_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业名称", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "enterprise_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业性质", "control": "select", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "国有,私企", "maxLength": 64, "metaType": "String", "name": "enterprise_property", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "行业代码", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "industry_code", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业类型", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "produce:产生企业,dispose:处置企业,transfer:运输企业", "maxLength": 64, "metaType": "String", "name": "enterprise_type", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "enterprise_id", "nodeIdName": "enterprise_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "运输企业", "column": 6, "control": "chosen", "dataType": "wordbook", "dictMode": "all", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "transfer_enterprise", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"enterprise_id": "01575a100f718a8ae70457565eca0300", "enterprise_name": "宁波安自富国际物流有限公司", "enterprise_property": "私企", "enterprise_type": "transfer", "industry_code": ""}], "count": 1, "exists": false}, "extend": "", "handle": "single", "icon": "", "id": "016bd4394bd88a28906d6bcf6b810031", "layout": "", "legend": "视图", "mode": "table", "name": "企业字典（运输企业）", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "企业主键", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "enterprise_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业名称", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "enterprise_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业性质", "control": "select", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "国有,私企", "maxLength": 64, "metaType": "String", "name": "enterprise_property", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "行业代码", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "industry_code", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业类型", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "produce:产生企业,dispose:处置企业,transfer:运输企业", "maxLength": 64, "metaType": "String", "name": "enterprise_type", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "enterprise_id", "nodeIdName": "enterprise_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "接受单位", "column": 6, "control": "chosen", "dataType": "wordbook", "dictMode": "all", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "dispose_enterprise", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"enterprise_id": "0156695406328a8afbb3566926740034", "enterprise_name": "宜兴市迈克化工有限公司", "enterprise_property": "私企", "enterprise_type": "dispose", "industry_code": ""}], "count": 1, "exists": false}, "extend": "", "handle": "single", "icon": "", "id": "016bd4391e7e8a28906d6bcf6b810028", "layout": "", "legend": "视图", "mode": "table", "name": "企业字典（处置企业）", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "企业主键", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "enterprise_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业名称", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "enterprise_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业性质", "control": "select", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "国有,私企", "maxLength": 64, "metaType": "String", "name": "enterprise_property", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "行业代码", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "industry_code", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业类型", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "produce:产生企业,dispose:处置企业,transfer:运输企业", "maxLength": 64, "metaType": "String", "name": "enterprise_type", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "enterprise_id", "nodeIdName": "enterprise_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "五联单号", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "山东省内转移无需填写", "list": "", "maxLength": 64, "metaType": "String", "name": "five_bills_code", "params": {"control": {"events": {"change:dispose_enterprise": "hide|01783ddecaea8a87d89c783ddab70005", "change:produce_enterprise": "hide|01783ddecaea8a87d89c783ddab70005"}}}, "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "承运人", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "transfer_person", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "废物运输车牌号", "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "car_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"car_code": "苏B96268", "car_id": "0156789405208a8afbb45673e5b40110", "car_type": "槽罐车", "note": "", "region_id": "0155723237938a8ae61a5570bbef10e4", "status": "有效", "transfer_code": ""}], "count": 1, "exists": false}, "extend": "", "handle": "", "icon": "", "id": "0154370cf0088a8ae61a538908b40bfe", "layout": "", "legend": "视图", "mode": "table", "name": "车辆字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "车辆主键", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "car_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "车牌号", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "car_code", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "车辆类型", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "槽罐车,中型卡车,大型卡车,特级车辆,重型厢式货车,重型普通货车,重型仓栅式货车,重型半挂牵引车,管道输送", "maxLength": 64, "metaType": "String", "name": "car_type", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "运输证号", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "transfer_code", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "有效期", "control": "date", "dataType": "date", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "valid_date", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "备注", "control": "textarea", "dataType": "text", "hidden": "list", "i18nCode": "", "legend": "", "list": "", "maxLength": 655350, "metaType": "Text", "name": "note", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "状态", "control": "select", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "有效,无效", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "system": "<PERSON><PERSON>ly", "type": "value", "unique": false, "value": "有效", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "区域主键", "column": 6, "control": "", "dataType": "id", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "region_id", "remind": "", "required": false, "sort": false, "system": "<PERSON><PERSON>ly", "type": "value", "unique": false, "value": "0155723237938a8ae61a5570bbef10e4", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "car_id", "nodeIdName": "car_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "车卡", "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "card_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [], "count": 0, "exists": false}, "extend": "", "handle": "", "icon": "", "id": "0155b4e445ce8a8ae61a55b07772058e", "layout": "", "legend": "IC卡信息视图", "mode": "table", "name": "可用车卡字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "主键", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "card_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "<PERSON><PERSON>ly", "alias": "物理编号", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "card_code", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": true, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "卡类型", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "car:车卡,person:员工卡", "maxLength": 64, "metaType": "String", "name": "card_type", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "名称", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "card_name", "remind": "", "required": true, "system": "none", "type": "value", "unique": true, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "有效期", "control": "date", "dataType": "date", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "card_validity", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "绑定对象", "control": "text", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "card_object", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "卡状态", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "Y:是,N:否", "maxLength": 64, "metaType": "String", "name": "card_status", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "N", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "card_id", "nodeIdName": "card_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "车卡", "control": "", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "card_code", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "车卡", "control": "", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "card_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "write", "alias": "电子锁", "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "lock_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": []}, "extend": "", "handle": "", "icon": "", "id": "0155b4e6a4c78a8ae61a55b0777205a2", "layout": "", "legend": "视图", "mode": "table", "name": "可用电子锁字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "主键", "control": "", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "device_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": true, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "设备类型", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "handset:手持机,scale:磅秤,lock:电子锁", "maxLength": 64, "metaType": "String", "name": "device_type", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "设备名称", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "device_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "设备编码", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "device_code", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "设备标识", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "device_identifies", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "MAC", "control": "", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "mac", "remind": "FF:FF:FF:FF:FF:FF", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "状态", "control": "chosen", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "valid:有效,invalid:无效", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "valid", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "备注", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "note", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "device_id", "nodeIdName": "device_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "电子锁", "control": "", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "lock_code", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "电子锁回收确认时间", "control": "datetime", "dataType": "time", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "lock_recycle_time", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "运输起点", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "transfer_start_position", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "运输终点", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "transfer_end_position", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "过磅单号", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "weight_record_code", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "皮重(KG)", "control": "text", "dataType": "number", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 20, "metaType": "Number", "name": "tare_weight", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "毛重(KG)", "control": "text", "dataType": "number", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 20, "metaType": "Number", "name": "gross_weight", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "净重(KG)", "control": "text", "dataType": "number", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 20, "metaType": "Number", "name": "weight", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "空车过磅时间", "control": "datetime", "dataType": "time", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "first_time", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "空车过磅点标识", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "first_node", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "空车过磅点名称", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "first_node_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "重车过磅时间", "control": "datetime", "dataType": "time", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "second_time", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "重车过磅点标识", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "second_node", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "重车过磅点名称", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "second_node_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "备注", "control": "textarea", "dataType": "text", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 655350, "metaType": "Text", "name": "note", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "待转移总数", "control": "text", "dataType": "integer", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "wait_count", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "通过总数", "control": "text", "dataType": "integer", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "pass_count", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "退回总数", "control": "text", "dataType": "integer", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "back_count", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "状态", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "function(value, row){\r\n\tvar style = 'style=\"color:red;font-weight:bold;\" ';    \r\n    var bpmStatus = row['bpm_status'];\r\n    if(bpmStatus == \"weight\" || bpmStatus == \"print\" || bpmStatus == \"upload\") {\r\n\t    var transferTime = row['transfer_time']; \r\n\t    style = ((typeof(transferTime) == \"undefined\") || (transferTime == ''))\r\n        ? style : '';\r\n\t    return '<span ' +style+ '>'+value+'</span>';\r\n\t} else {\r\n\t\treturn value;\r\n\t}    \r\n}", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "finish:审批完成,contact:联系外运,out-store:已出库,weight:已过磅,print:联单已打印,upload:联单已上传", "maxLength": 64, "metaType": "String", "name": "bpm_status", "remind": "", "required": false, "sort": false, "system": "<PERSON><PERSON>ly", "type": "value", "unique": false, "value": "draft", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "更新时间", "control": "", "dataType": "time", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "update_time", "remind": "", "required": false, "sort": false, "system": "write", "type": "value", "unique": false, "value": "1756167546658", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "五联单扫描件", "column": 12, "control": "file", "dataType": "file", "dictMode": "", "display": "", "events": "", "format": "function(value, row){\n  if (row.file !== undefined && row.file !== null && row.file != '') {\n    var url = 'hwms/api/hwit-paper/'  + row.transfer_id + '?_mode=attachment&bust=' + (new Date()).getTime();\n    return '联单附件：<a href=\"' + url +  '\" target=\"_blank\">点击下载</a>';\n  } else {\n    return '未上传';\n  }\n}", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "file", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "主单据", "control": "text", "dataType": "id", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "master_transfer_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "转移明细", "control": "", "dataType": "hwms_transfer_detail", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 512, "metaType": "model", "name": "detail", "remind": "", "required": false, "sort": false, "system": "none", "type": "collection", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "出门证", "control": "", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "out_paper", "remind": "", "required": false, "sort": false, "system": "none", "type": "link", "unique": false, "value": "https://frqas.whchem.com:8043/webroot/decision/view/report?viewlet=whchem/it/ppit/prj/hwms/A1出门证.cpt&transferId=<%=transfer_id%>&__bypagesize__=false", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "发送派车信息", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "用于系统判断是否向LMS发送派车信息", "list": "1:是,0:否", "maxLength": 64, "metaType": "String", "name": "is_send_info", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "1", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "年份", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "year", "remind": "", "required": false, "sort": false, "system": "<PERSON><PERSON>ly", "type": "value", "unique": false, "value": "2025", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "相关附件", "control": "", "dataType": "hwms_file", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "转移单附件——主要为过磅图片", "list": "", "maxLength": 512, "metaType": "model", "name": "attachment", "remind": "", "required": false, "sort": false, "system": "none", "type": "collection", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "所属公司", "column": 6, "control": "select", "dataType": "wordbook", "dictMode": "all", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "company_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"org_alias": "", "org_id": "00000017", "org_name": "万华化学（宁波）有限公司", "org_stitle": "", "parent_org_id": "", "sort_num": 130, "status": ""}], "count": 1, "exists": false}, "extend": "", "handle": "single", "icon": "", "id": "014cfe019cdaf9458a5d4cfdb2d00276", "layout": "", "legend": "", "mode": "tree", "name": "组织机构字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "组织主键", "column": 6, "control": "", "dataType": "uuid", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_org_id", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "org_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织名称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_org_name", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织别名", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_alias", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_alias", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织简称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_abbreviation", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_stitle", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "父组织", "column": 6, "control": "", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_parent_org", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "parent_org_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织排序", "column": 6, "control": "text", "dataType": "integer", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_sort_num", "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "sort_num", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "数据状态", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_status", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "org_id", "nodeIdName": "org_id", "parentIdName": "parent_org_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "00000017", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "所属事业部", "column": 6, "control": "select", "dataType": "wordbook", "dictMode": "all", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "division_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"org_alias": "", "org_id": "00000017", "org_name": "万华化学（宁波）有限公司", "org_stitle": "", "parent_org_id": "", "sort_num": 130, "status": ""}], "count": 1, "exists": false}, "extend": "", "handle": "single", "icon": "", "id": "014cfe019cdaf9458a5d4cfdb2d00276", "layout": "", "legend": "", "mode": "tree", "name": "组织机构字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "组织主键", "column": 6, "control": "", "dataType": "uuid", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_org_id", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "org_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织名称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_org_name", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织别名", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_alias", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_alias", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织简称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_abbreviation", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_stitle", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "父组织", "column": 6, "control": "", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_parent_org", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "parent_org_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织排序", "column": 6, "control": "text", "dataType": "integer", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_sort_num", "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "sort_num", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "数据状态", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_status", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "org_id", "nodeIdName": "org_id", "parentIdName": "parent_org_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "00000017", "verifyRule": "", "virtual": "false"}], "filters": [{"alias": "状态", "control": "select", "isRange": false, "name": "bpm_status", "required": false, "value": ""}, {"alias": "申请单号", "control": "text", "isRange": false, "name": "apply_code", "required": false, "value": ""}, {"alias": "五联单号", "control": "text", "isRange": false, "name": "five_bills_code", "required": false, "value": ""}, {"alias": "废物运输车牌号", "control": "chosen", "isRange": false, "name": "car_id", "required": false, "value": ""}, {"alias": "申请人", "control": "text", "isRange": false, "name": "user_name", "required": false, "value": ""}, {"alias": "固废名称", "control": "text", "isRange": false, "name": "waste_name", "required": false, "value": ""}, {"alias": "申请日期", "control": "daterange", "isRange": false, "name": "apply_date", "required": false, "value": ""}, {"alias": "车卡", "control": "text", "isRange": false, "name": "card_name", "required": false, "value": ""}, {"alias": "电子锁", "control": "text", "isRange": false, "name": "lock_code", "required": false, "value": ""}, {"alias": "过磅单号", "control": "text", "isRange": false, "name": "weight_record_code", "required": false, "value": ""}], "idName": "transfer_id", "nodeIdName": "transfer_id"}, "setting": "", "template": "", "type": "standard"}